# Koyeb-Based Preview Implementation (Docker + HTTP Code Injection)

## Overview

This document outlines the migration from Docker-based preview builds to Koyeb-based ephemeral dev server previews using pre-built Docker images with HTTP-based code injection. The new system will provide sub-5-second preview functionality with 5-minute lifespans.

## Architecture Flow

```
Frontend (/upload) → Backend → Koyeb Service Creation → HTTP Code Injection → Dev Server Ready
     ↓                ↓              ↓                        ↓                    ↓
  File Tree      Convert to      Deploy Container        POST /inject-code    Preview URL
   (JSON)        File Map         (~2-3 seconds)         (~1 second)        (Sub-5 seconds)
```

## Current System Analysis

### Current Architecture

- **Upload Endpoint**: `POST /upload` in `apps/worker/src/server.ts`
- **Build Process**: Docker containers with esbuild/SWC compilation
- **Storage**: File-based output storage with static builds
- **Preview**: Static HTML files served from build output
- **Cleanup**: Manual container management

### Current Flow

1. Frontend sends `updatedFileTree` to `/upload`
2. Worker creates Docker container with pre-installed dependencies
3. Files are written to container filesystem
4. Build process runs (esbuild/SWC compilation)
5. Static files generated and stored
6. Preview URL returned pointing to static files

## Core Concept

Instead of uploading directories, we use:

1. **Pre-built Docker image** with Next.js + all dependencies pre-installed
2. **HTTP API endpoint** in container for receiving AI-generated code
3. **Instant deployment** since no build/upload time needed
4. **Code injection** after container is running
5. **Sub-5-second total time** from request to preview

## Key Components

### 1. Pre-built Docker Base Image

```dockerfile
# apps/worker/docker/nextjs-base/Dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies globally for faster access
RUN npm install -g pnpm@8

# Create base Next.js project with all common dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy base template files
COPY . .

# Install code injection server dependencies
RUN pnpm add express cors fs-extra

# Expose ports
EXPOSE 3000 8080

# Copy startup script
COPY startup.js ./
RUN chmod +x startup.js

# Start with code injection server
CMD ["node", "startup.js"]
```

### 2. Code Injection Server

```javascript
// apps/worker/docker/nextjs-base/startup.js
const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json({ limit: '50mb' }));

let devServerProcess = null;

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    devServer: !!devServerProcess,
    timestamp: new Date().toISOString(),
  });
});

// Code injection endpoint
app.post('/inject-code', async (req, res) => {
  try {
    const { files } = req.body;

    if (!files || typeof files !== 'object') {
      return res.status(400).json({ error: 'Files object required' });
    }

    console.log(`Injecting ${Object.keys(files).length} files...`);

    // Kill existing dev server
    if (devServerProcess) {
      devServerProcess.kill('SIGTERM');
      devServerProcess = null;
    }

    // Write all files
    for (const [filePath, content] of Object.entries(files)) {
      const fullPath = path.join('/app', filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content || '');
    }

    console.log('Files written successfully');

    // Start dev server
    devServerProcess = spawn('pnpm', ['dev'], {
      cwd: '/app',
      stdio: 'pipe',
      env: { ...process.env, PORT: '3000' },
    });

    devServerProcess.stdout.on('data', (data) => {
      console.log(`Dev server: ${data}`);
    });

    devServerProcess.stderr.on('data', (data) => {
      console.error(`Dev server error: ${data}`);
    });

    devServerProcess.on('close', (code) => {
      console.log(`Dev server exited with code ${code}`);
      devServerProcess = null;
    });

    // Wait a moment for server to start
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Code injected and dev server started',
        filesCount: Object.keys(files).length,
      });
    }, 2000);
  } catch (error) {
    console.error('Code injection failed:', error);
    res.status(500).json({
      error: 'Code injection failed',
      details: error.message,
    });
  }
});

// Start code injection server
app.listen(8080, () => {
  console.log('Code injection API listening on port 8080');
  console.log('Waiting for code injection...');
});
```

### 3. Base Package.json with All Dependencies

```json
{
  "name": "koyeb-nextjs-preview",
  "version": "1.0.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "lucide-react": "^0.263.0",
    "clsx": "^2.0.0",
    "class-variance-authority": "^0.7.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0",
    "framer-motion": "^10.0.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0"
  }
}
```

### 4. Koyeb Service Manager

```typescript
// apps/worker/src/koyeb/koyeb-service.ts

interface KoyebApp {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface KoyebService {
  id: string;
  name: string;
  app_id: string;
  status: string;
  latest_deployment_id: string;
  created_at: string;
  updated_at: string;
}

interface KoyebDeployment {
  id: string;
  service_id: string;
  status: string;
  messages: string[];
  definition: {
    name: string;
    type: string;
    regions: string[];
    instance_types: {
      type: string;
    };
    docker: {
      image: string;
    };
    ports: Array<{
      port: number;
      protocol: string;
    }>;
    env: Array<{
      key: string;
      value: string;
    }>;
    health_checks: Array<{
      http: {
        port: number;
        path: string;
      };
    }>;
    scaling: {
      min: number;
      max: number;
    };
  };
}

export class KoyebPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://app.koyeb.com/v1';
  private dockerImage: string;

  constructor(apiKey: string, dockerImage: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Koyeb API error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  async createApp(appName: string): Promise<KoyebApp> {
    return this.makeRequest<{ app: KoyebApp }>('/apps', 'POST', {
      name: appName,
    }).then((response) => response.app);
  }

  async createPreviewService(
    projectData: any,
  ): Promise<{ serviceId: string; appId: string; serviceUrl: string }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const serviceName = `preview-service-${timestamp}-${randomId}`;

    // Step 1: Create App
    const app = await this.createApp(appName);

    // Step 2: Create Service with Docker deployment
    const servicePayload = {
      app_id: app.id,
      definition: {
        name: serviceName,
        type: 'WEB',
        regions: ['fra'],
        instance_types: {
          type: 'nano',
        },
        docker: {
          image: this.dockerImage,
        },
        ports: [
          {
            port: 3000,
            protocol: 'http',
          },
          {
            port: 8080,
            protocol: 'http',
          },
        ],
        env: [
          {
            key: 'NODE_ENV',
            value: 'development',
          },
        ],
        health_checks: [
          {
            http: {
              port: 8080,
              path: '/health',
            },
          },
        ],
        scaling: {
          min: 1,
          max: 1,
        },
      },
    };

    const service = await this.makeRequest<{ service: KoyebService }>(
      '/services',
      'POST',
      servicePayload,
    ).then((response) => response.service);

    // Step 3: Wait for service to be ready
    const serviceUrl = await this.waitForServiceReady(service.id);

    // Schedule auto-destruction after 5 minutes
    setTimeout(
      () => {
        this.destroyService(service.id, app.id);
      },
      parseInt(process.env.PREVIEW_TIMEOUT || '300000'),
    );

    return {
      serviceId: service.id,
      appId: app.id,
      serviceUrl,
    };
  }

  async getService(serviceId: string): Promise<KoyebService> {
    return this.makeRequest<{ service: KoyebService }>(
      `/services/${serviceId}`,
    ).then((response) => response.service);
  }

  async getDeployment(deploymentId: string): Promise<KoyebDeployment> {
    return this.makeRequest<{ deployment: KoyebDeployment }>(
      `/deployments/${deploymentId}`,
    ).then((response) => response.deployment);
  }

  async waitForServiceReady(serviceId: string): Promise<string> {
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes max wait

    while (attempts < maxAttempts) {
      try {
        const service = await this.getService(serviceId);

        if (service.latest_deployment_id) {
          const deployment = await this.getDeployment(
            service.latest_deployment_id,
          );

          if (deployment.status === 'HEALTHY') {
            // Get the public URL
            const publicUrl = await this.getServiceUrl(serviceId);
            return publicUrl;
          }

          if (
            deployment.status === 'ERROR' ||
            deployment.status === 'STOPPED'
          ) {
            throw new Error(
              `Service deployment failed: ${deployment.messages?.join(', ')}`,
            );
          }

          console.log(`Service ${serviceId} status: ${deployment.status}`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    throw new Error('Service failed to become ready within timeout');
  }

  async getServiceUrl(serviceId: string): Promise<string> {
    const service = await this.getService(serviceId);
    const deployment = await this.getDeployment(service.latest_deployment_id);

    // Koyeb automatically assigns URLs based on service name and region
    const appId = service.app_id.split('-')[0];
    const serviceName = deployment.definition.name;

    return `https://${serviceName}-${appId}.koyeb.app`;
  }

  async injectCode(
    serviceUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    const injectionUrl = `${serviceUrl}:8080/inject-code`;

    const response = await fetch(injectionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Koyeb-Preview-System/1.0',
      },
      body: JSON.stringify({ files }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Code injection failed: ${response.status} - ${errorText}`,
      );
    }

    const result = await response.json();
    console.log('Code injection successful:', result.message);
  }

  async destroyService(serviceId: string, appId: string): Promise<void> {
    try {
      await this.makeRequest(`/services/${serviceId}`, 'DELETE');
      console.log(`Service ${serviceId} deleted successfully`);

      await this.makeRequest(`/apps/${appId}`, 'DELETE');
      console.log(`App ${appId} deleted successfully`);
    } catch (error) {
      console.error(
        `Failed to destroy service ${serviceId} and app ${appId}:`,
        error,
      );
    }
  }
}
```

### 5. File Tree to Files Converter

```typescript
// apps/worker/src/utils/file-converter.ts
export function convertFileTreeToFiles(fileTree: any): Record<string, string> {
  const files: Record<string, string> = {};

  function processNode(node: any, currentPath: string = '') {
    if (node.type === 'file') {
      files[currentPath] = node.content || '';
    } else if (node.type === 'directory' && node.children) {
      Object.entries(node.children).forEach(([name, child]) => {
        const newPath = currentPath ? `${currentPath}/${name}` : name;
        processNode(child, newPath);
      });
    }
  }

  processNode(fileTree);
  return files;
}
```

## Implementation Steps

### Phase 1: Docker Image Setup

1. **Build Base Docker Image**

```bash
# Create directory structure
mkdir -p apps/worker/docker/nextjs-base
cd apps/worker/docker/nextjs-base

# Create all the files shown above (Dockerfile, startup.js, package.json)
# Then build and push to registry
docker build -t your-registry/nextjs-preview-base:latest .
docker push your-registry/nextjs-preview-base:latest
```

2. **Environment Configuration**

```bash
# Add to .env
KOYEB_API_KEY=your_koyeb_api_key
PREVIEW_TIMEOUT=300000  # 5 minutes
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest
```

### Phase 2: Backend Implementation

1. **Update Upload Endpoint**

```typescript
// apps/worker/src/server.ts - Update upload endpoint
import { KoyebPreviewManager } from './koyeb/koyeb-service';
import { convertFileTreeToFiles } from './utils/file-converter';

app.post('/upload', async (req, res) => {
  try {
    const projectData = req.body;
    const validationError = validateProjectData(projectData);
    if (validationError) {
      return res.status(400).json({ error: validationError });
    }

    const jobId = uuidv4();
    const koyebManager = new KoyebPreviewManager(
      process.env.KOYEB_API_KEY!,
      process.env.DOCKER_IMAGE!,
    );

    // Step 1: Create Koyeb service (2-3 seconds)
    const { serviceId, appId, serviceUrl } =
      await koyebManager.createPreviewService(projectData);

    // Step 2: Convert file tree to files map
    const files = convertFileTreeToFiles(projectData);

    // Step 3: Inject code into running container (1 second)
    await koyebManager.injectCode(serviceUrl, files);

    res.json({
      jobId,
      serviceId,
      appId,
      message: 'Preview service created and code injected',
      previewUrl: serviceUrl, // Direct dev server URL
      wsUrl: `${serviceUrl.replace('http', 'ws')}/ws`,
    });
  } catch (err) {
    console.error('Upload error:', err);
    res.status(500).json({
      error: 'Preview creation failed',
      details: err instanceof Error ? err.message : String(err),
    });
  }
});
```

### Phase 3: Frontend Updates

1. **No Changes Needed to Upload Logic**

```typescript
// apps/admin/src/features/chat/main.tsx
// The existing upload logic works perfectly:

const response = (await api.post('/upload', updatedFileTree, {
  baseURL: `http://localhost:${env.SERVER_PORT}`,
})) as UploadResponse;

const previewUrl = response.previewUrl; // No need to append /index.html
setPreviewUrl(previewUrl);
```

2. **Keep Full Server Capabilities**

```typescript
// apps/admin/src/features/chat/utils/convert.ts
// NO CHANGES NEEDED - keep all files including:
// ✅ API routes (route.ts files)
// ✅ Middleware files
// ✅ Original package.json scripts
// ✅ All server-side functionality

// The modifyFilesForPreview function can remain as-is or be simplified
// since we now have a full dev server that supports everything
```

## Performance & Benefits

### Speed Comparison

| Step                 | Current Docker    | New Koyeb + Code Injection |
| -------------------- | ----------------- | -------------------------- |
| **Container Start**  | ~10-15 seconds    | ~2-3 seconds               |
| **Build Process**    | ~30-60 seconds    | ❌ No build needed         |
| **Code Injection**   | ❌ Not applicable | ~1 second                  |
| **Dev Server Start** | ~5-10 seconds     | ~1 second                  |
| **Total Time**       | ~45-85 seconds    | **~4-5 seconds**           |

### Key Benefits

1. **Sub-5-Second Previews**

   - Pre-built Docker image with all dependencies
   - No build/compile time needed
   - Instant code injection via HTTP
   - Fast container startup on Koyeb

2. **Full Server Capabilities**

   - API routes work perfectly
   - Middleware supported
   - SSR/SSG capabilities
   - Database connections
   - File uploads
   - Authentication flows

3. **Scalability**

   - No local Docker management
   - Koyeb handles infrastructure
   - Multiple concurrent previews
   - Automatic cleanup after 5 minutes

4. **Cost Efficiency**

   - Pay only for 5-minute usage
   - Nano instances (~$0.000115 per preview)
   - No persistent infrastructure costs

5. **Reliability**
   - Distributed Koyeb infrastructure
   - Health checks and auto-restart
   - Automatic service cleanup
   - Error handling and fallbacks

## Required Environment Variables

```bash
# Minimal configuration needed
KOYEB_API_KEY=your_koyeb_api_key
PREVIEW_TIMEOUT=300000  # 5 minutes
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest

# Existing
SERVER_PORT=8001
WORKER_PORT=8002
```

## Migration Timeline

1. **Week 1**: Build and test Docker base image locally
2. **Week 2**: Push to registry, implement KoyebPreviewManager
3. **Week 3**: Update backend upload endpoint, test integration
4. **Week 4**: Production deployment and monitoring
5. **Week 5**: Performance optimization and cleanup automation

This approach gives you the best of both worlds: the speed of pre-built images with the flexibility of dynamic code injection, resulting in sub-5-second preview generation!
`````

#### 2. Temporary Directory Manager

````typescript
// apps/worker/src/utils/temp-directory.ts
import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import * as tar from 'tar';

export class TempDirectoryManager {
  private tempBasePath: string;

  constructor(tempBasePath: string = '/tmp/koyeb-previews') {
    this.tempBasePath = tempBasePath;
    fs.ensureDirSync(this.tempBasePath);
  }

  generateHash(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  async createProjectDirectory(projectData: ProjectData): Promise<string> {
    const hash = this.generateHash();
    const projectPath = path.join(this.tempBasePath, hash);

    // Create directory
    await fs.ensureDir(projectPath);

    // Write all files from projectData
    await this.writeProjectFiles(projectData, projectPath);

    // Add package.json with dev command if not exists
    await this.ensurePackageJson(projectPath);

    return projectPath;
  }

  private async writeProjectFiles(node: any, basePath: string, currentPath: string = ''): Promise<void> {
    if (node.type === 'file') {
      const filePath = path.join(basePath, currentPath);
      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, node.content || '');
    } else if (node.type === 'directory' && node.children) {
      for (const [name, child] of Object.entries(node.children)) {
        const newPath = path.join(currentPath, name);
        await this.writeProjectFiles(child, basePath, newPath);
      }
    }
  }

  private async ensurePackageJson(projectPath: string): Promise<void> {
    const packageJsonPath = path.join(projectPath, 'package.json');

    if (!await fs.pathExists(packageJsonPath)) {
      // Create default package.json for Next.js
      const defaultPackageJson = {
        name: 'koyeb-preview',
        version: '1.0.0',
        scripts: {
          dev: 'next dev',
          build: 'next build',
          start: 'next start'
        },
        dependencies: {
          next: '^14.0.0',
          react: '^18.2.0',
          'react-dom': '^18.2.0'
        },
        devDependencies: {
          '@types/node': '^20.0.0',
          '@types/react': '^18.2.0',
          '@types/react-dom': '^18.2.0',
          typescript: '^5.0.0'
        }
      };

      await fs.writeJson(packageJsonPath, defaultPackageJson, { spaces: 2 });
    }
  }

  async createTarball(projectPath: string): Promise<string> {
    const tarballPath = `${projectPath}.tar.gz`;

    await tar.create(
      {
        gzip: true,
        file: tarballPath,
        cwd: path.dirname(projectPath),
      },
      [path.basename(projectPath)]
    );

    return tarballPath;
  }

  async cleanup(projectPath: string): Promise<void> {
    try {
      await fs.remove(projectPath);

      // Also remove tarball if exists
      const tarballPath = `${projectPath}.tar.gz`;
      if (await fs.pathExists(tarballPath)) {
        await fs.remove(tarballPath);
      }
    } catch (error) {
      console.error(`Failed to cleanup ${projectPath}:`, error);
    }
  }

  async cleanupOldDirectories(maxAgeMinutes: number = 10): Promise<void> {
    try {
      const entries = await fs.readdir(this.tempBasePath);
      const now = Date.now();

      for (const entry of entries) {
        const entryPath = path.join(this.tempBasePath, entry);
        const stats = await fs.stat(entryPath);
        const ageMinutes = (now - stats.mtime.getTime()) / (1000 * 60);

        if (ageMinutes > maxAgeMinutes) {
          await this.cleanup(entryPath);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old directories:', error);
    }
  }
}

### Koyeb API Integration Details

#### API Authentication
The Koyeb API uses Bearer token authentication. You'll need:
1. **API Token**: Generate from Koyeb dashboard → Settings → API

#### Key API Endpoints Used
```typescript
// Base URL: https://app.koyeb.com/v1

// Archives (for directory upload)
POST /archives               // Get signed upload URL (requires: size)

// Apps Management
POST /apps                   // Create app (requires: name)
DELETE /apps/{id}            // Delete app (no body)

// Services Management
POST /services               // Create service (requires: app_id, definition)
GET /services/{id}           // Get service details
DELETE /services/{id}        // Delete service (no body)

// Deployments
GET /deployments/{id}        // Get deployment status
`````

#### Archive Upload Process

Based on the API reference, archive upload is a 2-step process:

1. **Get Signed Upload URL**:

   ```typescript
   POST /v1/archives
   Body: { "size": 1234567 }  // Archive size in bytes
   Response: { "upload_url": "https://...", "archive_id": "archive-123" }
   ```

2. **Upload File to Signed URL**:
   ```typescript
   PUT {upload_url}
   Body: <binary file data>
   Headers: { "Content-Type": "application/gzip" }
   ```

#### Rate Limits & Best Practices

- **Rate Limit**: 100 requests per minute per API token
- **Timeout**: 30 seconds for service creation
- **Retry Logic**: Exponential backoff for failed requests
- **Cleanup**: Always delete services and apps to avoid charges

### Implementation Steps

#### Phase 1: Koyeb Integration Setup

1. **Environment Configuration**

```bash
# Add to .env
KOYEB_API_KEY=your_koyeb_api_key
PREVIEW_TIMEOUT=300000  # 5 minutes
TEMP_DIRECTORY_PATH=/tmp/koyeb-previews  # Temporary directory for project files

# Get your API key from: https://app.koyeb.com/account/api
```

2. **No Additional Dependencies Required**

```bash
# No SDK needed - using direct HTTP API calls with fetch
# Only need node-fetch if not using Node.js 18+ (which has built-in fetch)
```

3. **Create Koyeb Directory Upload Manager**

```typescript
// apps/worker/src/koyeb/koyeb-service.ts

export class KoyebDirectoryUploadManager {
  private apiKey: string;
  private baseUrl = 'https://app.koyeb.com/v1';
  private tempManager: TempDirectoryManager;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.tempManager = new TempDirectoryManager();
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
    isFormData: boolean = false,
  ): Promise<T> {
    const headers: Record<string, string> = {
      Authorization: `Bearer ${this.apiKey}`,
    };

    if (!isFormData) {
      headers['Content-Type'] = 'application/json';
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers,
      body: isFormData ? body : body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Koyeb API error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  async createApp(appName: string): Promise<KoyebApp> {
    return this.makeRequest<{ app: KoyebApp }>('/apps', 'POST', {
      name: appName,
    }).then((response) => response.app);
  }

  async getUploadUrl(
    archiveSize: number,
  ): Promise<{ upload_url: string; archive_id: string }> {
    return this.makeRequest<{ upload_url: string; archive_id: string }>(
      '/archives',
      'POST',
      {
        size: archiveSize,
      },
    );
  }

  async uploadArchive(tarballPath: string): Promise<string> {
    const stats = await fs.stat(tarballPath);
    const archiveSize = stats.size;

    // Get signed upload URL
    const { upload_url, archive_id } = await this.getUploadUrl(archiveSize);

    // Upload file directly to the signed URL
    const fileBuffer = await fs.readFile(tarballPath);
    const uploadResponse = await fetch(upload_url, {
      method: 'PUT',
      body: fileBuffer,
      headers: {
        'Content-Type': 'application/gzip',
      },
    });

    if (!uploadResponse.ok) {
      throw new Error(`Archive upload failed: ${uploadResponse.statusText}`);
    }

    return archive_id;
  }

  async createPreviewService(
    projectData: ProjectData,
  ): Promise<{ serviceId: string; appId: string }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const serviceName = `preview-service-${timestamp}-${randomId}`;
    const archiveName = `preview-archive-${timestamp}-${randomId}`;

    let projectPath: string | null = null;
    let tarballPath: string | null = null;

    try {
      // Step 1: Create temporary directory with project files
      projectPath = await this.tempManager.createProjectDirectory(projectData);

      // Step 2: Create tarball
      tarballPath = await this.tempManager.createTarball(projectPath);

      // Step 3: Upload archive to Koyeb
      const archiveId = await this.uploadArchive(tarballPath);

      // Step 4: Create App
      const app = await this.createApp(appName);

      // Step 5: Create Service with Archive deployment
      const servicePayload = {
        app_id: app.id,
        definition: {
          name: serviceName,
          type: 'WEB',
          regions: ['fra'],
          instance_types: {
            type: 'nano',
          },
          archive: {
            id: archiveId,
          },
          ports: [
            {
              port: 3000,
              protocol: 'http',
            },
          ],
          env: [
            {
              key: 'NODE_ENV',
              value: 'development',
            },
          ],
          scaling: {
            min: 1,
            max: 1,
          },
        },
      };

      const service = await this.makeRequest<{ service: KoyebService }>(
        '/services',
        'POST',
        servicePayload,
      ).then((response) => response.service);

      // Schedule auto-destruction after 5 minutes
      setTimeout(
        () => {
          this.destroyService(service.id, app.id);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || '300000'),
      );

      return { serviceId: service.id, appId: app.id };
    } finally {
      // Cleanup temporary files
      if (projectPath) {
        await this.tempManager.cleanup(projectPath);
      }
    }
  }

  async getService(serviceId: string): Promise<KoyebService> {
    return this.makeRequest<{ service: KoyebService }>(
      `/services/${serviceId}`,
    ).then((response) => response.service);
  }

  async getDeployment(deploymentId: string): Promise<KoyebDeployment> {
    return this.makeRequest<{ deployment: KoyebDeployment }>(
      `/deployments/${deploymentId}`,
    ).then((response) => response.deployment);
  }

  async waitForServiceReady(serviceId: string): Promise<string> {
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes max wait (build + start time)

    while (attempts < maxAttempts) {
      try {
        const service = await this.getService(serviceId);

        if (service.latest_deployment_id) {
          const deployment = await this.getDeployment(
            service.latest_deployment_id,
          );

          if (deployment.status === 'HEALTHY') {
            // Get the public URL
            const publicUrl = await this.getServiceUrl(serviceId);
            return publicUrl;
          }

          if (
            deployment.status === 'ERROR' ||
            deployment.status === 'STOPPED'
          ) {
            throw new Error(
              `Service deployment failed: ${deployment.messages?.join(', ')}`,
            );
          }

          console.log(`Service ${serviceId} status: ${deployment.status}`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    throw new Error('Service failed to become ready within timeout');
  }

  async getServiceUrl(serviceId: string): Promise<string> {
    // Get service details to find the public URL
    const service = await this.getService(serviceId);
    const deployment = await this.getDeployment(service.latest_deployment_id);

    // Koyeb automatically assigns URLs based on service name and region
    // Format: https://{service-name}-{app-id}.koyeb.app
    const appId = service.app_id.split('-')[0]; // Get short app ID
    const serviceName = deployment.definition.name;

    return `https://${serviceName}-${appId}.koyeb.app`;
  }

  async destroyService(serviceId: string, appId: string): Promise<void> {
    try {
      // Delete service first (no body required)
      await this.makeRequest(`/services/${serviceId}`, 'DELETE');
      console.log(`Service ${serviceId} deleted successfully`);

      // Then delete the app (no body required)
      await this.makeRequest(`/apps/${appId}`, 'DELETE');
      console.log(`App ${appId} deleted successfully`);
    } catch (error) {
      console.error(
        `Failed to destroy service ${serviceId} and app ${appId}:`,
        error,
      );
    }
  }

  async listServices(): Promise<KoyebService[]> {
    return this.makeRequest<{ services: KoyebService[] }>('/services').then(
      (response) => response.services,
    );
  }

  async cleanupOldServices(): Promise<void> {
    try {
      const services = await this.listServices();
      const now = Date.now();
      const fiveMinutesAgo = now - 5 * 60 * 1000;

      for (const service of services) {
        const createdAt = new Date(service.created_at).getTime();
        if (
          createdAt < fiveMinutesAgo &&
          service.name.startsWith('preview-service-')
        ) {
          console.log(`Cleaning up old service: ${service.id}`);
          await this.destroyService(service.id, service.app_id);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old services:', error);
    }
  }
}
```

#### Phase 2: Dependencies Installation

1. **Install Required Dependencies**

```bash
cd apps/worker
npm install tar fs-extra
# or
pnpm add tar fs-extra

# Types for TypeScript
npm install -D @types/tar
# or
pnpm add -D @types/tar
```

2. **No Base Images Required**

```bash
# No Docker images needed - Koyeb handles buildpack automatically
# The uploaded directory will be built using Koyeb's Node.js buildpack
```

#### Phase 3: Update Upload Endpoint

1. **Modify Worker Upload Handler**

```typescript
// apps/worker/src/server.ts - Update upload endpoint
app.post('/upload', async (req, res) => {
  try {
    const projectData = req.body as ProjectData;
    const validationError = validateProjectData(projectData);
    if (validationError) {
      return res.status(400).json({ error: validationError });
    }

    const jobId = uuidv4();
    const koyebManager = new KoyebDirectoryUploadManager(
      process.env.KOYEB_API_KEY!,
    );

    // Create Koyeb service with directory upload
    const { serviceId, appId } =
      await koyebManager.createPreviewService(projectData);

    // Wait for service to be ready (includes build time)
    const serviceUrl = await koyebManager.waitForServiceReady(serviceId);

    res.json({
      jobId,
      serviceId,
      appId,
      message: 'Preview service created and deployed',
      previewUrl: serviceUrl, // Direct dev server URL
      wsUrl: `${serviceUrl.replace('http', 'ws')}/ws`, // WebSocket for hot reload (if supported)
    });
  } catch (err) {
    console.error('Upload error:', err);
    res.status(500).json({
      error: 'Preview creation failed',
      details: err instanceof Error ? err.message : String(err),
    });
  }
});
```

2. **Add Cleanup Scheduler**

```typescript
// apps/worker/src/utils/cleanup-scheduler.ts
export class CleanupScheduler {
  private koyebManager: KoyebDirectoryUploadManager;
  private tempManager: TempDirectoryManager;

  constructor(koyebManager: KoyebDirectoryUploadManager) {
    this.koyebManager = koyebManager;
    this.tempManager = new TempDirectoryManager();
  }

  startPeriodicCleanup() {
    // Cleanup old services every 2 minutes
    setInterval(
      async () => {
        try {
          await this.koyebManager.cleanupOldServices();
        } catch (error) {
          console.error('Service cleanup failed:', error);
        }
      },
      2 * 60 * 1000,
    );

    // Cleanup old temp directories every 5 minutes
    setInterval(
      async () => {
        try {
          await this.tempManager.cleanupOldDirectories(10); // 10 minutes old
        } catch (error) {
          console.error('Temp directory cleanup failed:', error);
        }
      },
      5 * 60 * 1000,
    );
  }
}

// Initialize in server.ts
const cleanupScheduler = new CleanupScheduler(koyebManager);
cleanupScheduler.startPeriodicCleanup();
```

#### Phase 4: Frontend Updates

1. **Remove Static Export Modifications**

```typescript
// apps/admin/src/features/chat/utils/convert.ts
// Create new function for Koyeb dev server (no static export modifications needed)
export function modifyFilesForKoyebPreview(
  gitFiles: Record<string, string>,
  pageRoute?: string,
): Record<string, string> {
  const modifiedFiles = { ...gitFiles };

  // 1. NO package.json modifications needed - keep original build scripts
  // Since we're running a dev server, we don't need static export modifications

  // 2. Copy page route content to root page for preview (keep this)
  if (pageRoute && pageRoute !== '/') {
    const sourcePagePath = `src/app${pageRoute}/page.tsx`;
    const targetPagePath = `src/app/page.tsx`;

    if (modifiedFiles[sourcePagePath]) {
      modifiedFiles[targetPagePath] = modifiedFiles[sourcePagePath];
    } else {
      console.warn(`Source page not found: ${sourcePagePath}`);
    }
  }

  // 3. KEEP middleware.ts files - dev server can handle them
  // No need to remove middleware files since we have a full server

  // 4. KEEP route.ts files (API routes) - dev server supports them
  // API routes work perfectly fine in dev server mode

  return modifiedFiles;
}
```

2. **Update Chat Component to Use New Function**

```typescript
// apps/admin/src/features/chat/main.tsx
// Replace modifyFilesForPreview with modifyFilesForKoyebPreview

// OLD (for static exports):
const modifiedGitFiles = modifyFilesForPreview(currentGitFiles, pageRoute);

// NEW (for Koyeb dev server):
const modifiedGitFiles = modifyFilesForKoyebPreview(currentGitFiles, pageRoute);
```

3. **Update Preview URL Handling**

```typescript
// apps/admin/src/features/chat/main.tsx
// Update the preview URL logic to handle dev server directly
const previewUrl = response.previewUrl; // No need to append /index.html
setPreviewUrl(previewUrl);

// Set iframe src directly for immediate preview
if (previewRef.current) {
  previewRef.current.src = previewUrl;
}
```

4. **Optional: Add WebSocket Support for Hot Reload**

```typescript
// Optional: Connect to dev server WebSocket for hot reload
useEffect(() => {
  if (socketUrl) {
    const ws = new WebSocket(socketUrl);
    ws.onmessage = (event) => {
      // Handle hot reload messages
      if (event.data.includes('reload')) {
        if (previewRef.current) {
          previewRef.current.src = previewRef.current.src; // Force reload
        }
      }
    };

    return () => ws.close();
  }
}, [socketUrl]);
```

### Configuration & Environment

#### Required Environment Variables

```bash
# Koyeb Configuration (Direct API)
KOYEB_API_KEY=your_koyeb_api_key

# Preview Configuration
PREVIEW_TIMEOUT=300000  # 5 minutes in milliseconds
TEMP_DIRECTORY_PATH=/tmp/koyeb-previews  # Temporary directory for project files

# Existing
SERVER_PORT=8001
WORKER_PORT=8002
```

#### Minimal Koyeb Service Configuration

```typescript
// Minimal service creation payload based on API reference
const servicePayload = {
  app_id: 'app-id-here', // Required
  definition: {
    // Required
    name: 'service-name',
    type: 'WEB',
    regions: ['fra'], // Required
    instance_types: {
      type: 'nano', // Required
    },
    archive: {
      id: 'archive-id-here', // Required for archive deployment
    },
    ports: [
      // Required for WEB services
      {
        port: 3000,
        protocol: 'http',
      },
    ],
    env: [
      // Optional but useful
      {
        key: 'NODE_ENV',
        value: 'development',
      },
    ],
    scaling: {
      // Optional, defaults applied
      min: 1,
      max: 1,
    },
  },
};
```

### Benefits of New Architecture (Directory Upload)

1. **Performance**

   - Direct directory upload (no Docker image building)
   - Koyeb's optimized buildpack for fast builds
   - Dev server starts after build completion
   - No intermediate file storage required

2. **Scalability**

   - No local Docker container management
   - No base image maintenance required
   - Koyeb handles infrastructure scaling automatically
   - Multiple concurrent previews without resource conflicts

3. **Cost Efficiency**

   - Pay only for actual usage (5 minutes per preview)
   - Nano instances (~$0.000115 per preview)
   - No persistent infrastructure or storage costs
   - No container registry costs

4. **Reliability**

   - Automatic service cleanup after timeout
   - Built-in health checks and auto-restart
   - Distributed infrastructure with global CDN
   - No dependency on external container registries

5. **Simplicity**

   - No Docker images to build or maintain
   - No code injection complexity
   - Direct file upload to Koyeb
   - Automatic buildpack detection and building

6. **Full Server Capabilities**
   - **API Routes Support**: Can include and test API routes (`route.ts` files)
   - **Middleware Support**: Server-side middleware works normally
   - **SSR/SSG**: Full Next.js rendering capabilities
   - **Database Connections**: Can connect to databases and external services
   - **Authentication**: Server-side auth flows work properly
   - **File Uploads**: Can handle file upload endpoints
   - **WebSocket Support**: Real-time features work out of the box

### Comparison: Static Export vs Full Dev Server

| Feature                   | Current (Static Export)      | New (Koyeb Dev Server)       |
| ------------------------- | ---------------------------- | ---------------------------- |
| **API Routes**            | ❌ Removed (cause crashes)   | ✅ Fully supported           |
| **Middleware**            | ❌ Removed (incompatible)    | ✅ Fully supported           |
| **SSR/SSG**               | ❌ Static only               | ✅ Full Next.js capabilities |
| **Database**              | ❌ No server-side code       | ✅ Full database access      |
| **Authentication**        | ❌ Client-side only          | ✅ Server-side auth flows    |
| **File Uploads**          | ❌ Not possible              | ✅ Full upload support       |
| **Environment Variables** | ❌ Build-time only           | ✅ Runtime access            |
| **Hot Reload**            | ❌ Manual refresh            | ✅ Automatic updates         |
| **Build Time**            | ⚠️ Required for each preview | ✅ No build needed           |
| **Preview Speed**         | ⚠️ Slow (build + serve)      | ✅ Fast (direct serve)       |

### Frontend Code Changes Required

#### 1. **Update File Modification Function**

```typescript
// Before: Static export modifications
const modifiedGitFiles = modifyFilesForPreview(currentGitFiles, pageRoute);

// After: Minimal modifications for dev server
const modifiedGitFiles = modifyFilesForKoyebPreview(currentGitFiles, pageRoute);
```

#### 2. **What Gets Removed from Current Logic**

- ❌ `STATIC_PREVIEW=true` environment variable injection
- ❌ Removal of `route.ts` files (API routes)
- ❌ Removal of `middleware.ts` files
- ❌ Static export specific package.json modifications

#### 3. **What Gets Kept**

- ✅ Page route copying (for preview navigation)
- ✅ File filtering for ignored files
- ✅ Basic project structure

### Migration Timeline

1. **Week 1**: Set up Koyeb account, implement TempDirectoryManager
2. **Week 2**: Implement KoyebDirectoryUploadManager with archive upload
3. **Week 3**: Update worker upload endpoint and test directory upload
4. **Week 4**: Frontend updates (remove static export logic) and end-to-end testing
5. **Week 5**: Production deployment, monitoring, and cleanup automation

### Error Handling & Resilience

#### API Error Handling

```typescript
// apps/worker/src/koyeb/error-handler.ts
export class KoyebErrorHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) break;

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise((resolve) => setTimeout(resolve, delay));

        console.log(
          `Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`,
        );
      }
    }

    throw lastError!;
  }

  static isRetryableError(error: Error): boolean {
    const retryableStatuses = [429, 500, 502, 503, 504];
    const statusMatch = error.message.match(/(\d{3})/);

    if (statusMatch) {
      const status = parseInt(statusMatch[1]);
      return retryableStatuses.includes(status);
    }

    return false;
  }
}
```

#### Fallback Strategy

```typescript
// apps/worker/src/preview/preview-manager.ts
export class PreviewManager {
  private koyebManager: KoyebServiceManager;
  private dockerFallback: DockerPreviewManager; // Your existing system

  async createPreview(projectData: ProjectData): Promise<PreviewResponse> {
    try {
      // Try Koyeb first
      return await this.createKoyebPreview(projectData);
    } catch (error) {
      console.error('Koyeb preview failed, falling back to Docker:', error);

      // Fallback to existing Docker system
      return await this.dockerFallback.createPreview(projectData);
    }
  }

  private async createKoyebPreview(
    projectData: ProjectData,
  ): Promise<PreviewResponse> {
    return KoyebErrorHandler.withRetry(async () => {
      const serviceId =
        await this.koyebManager.createPreviewService(projectData);
      const serviceUrl = await this.koyebManager.waitForServiceReady(serviceId);

      const files = convertProjectDataToFiles(projectData);
      await this.koyebManager.injectCode(serviceUrl, files);

      return {
        jobId: serviceId,
        previewUrl: serviceUrl,
        wsUrl: `${serviceUrl.replace('http', 'ws')}/ws`,
        provider: 'koyeb',
      };
    });
  }
}
```

### Monitoring & Observability

#### Service Tracking

```typescript
// apps/worker/src/monitoring/koyeb-monitor.ts
export class KoyebMonitor {
  private metrics: Map<string, any> = new Map();

  trackServiceCreation(serviceId: string, startTime: number) {
    this.metrics.set(`service:${serviceId}`, {
      created: startTime,
      status: 'creating',
    });
  }

  trackServiceReady(serviceId: string, readyTime: number) {
    const service = this.metrics.get(`service:${serviceId}`);
    if (service) {
      service.ready = readyTime;
      service.startupTime = readyTime - service.created;
      service.status = 'ready';
    }
  }

  trackServiceDestroyed(serviceId: string, destroyTime: number) {
    const service = this.metrics.get(`service:${serviceId}`);
    if (service) {
      service.destroyed = destroyTime;
      service.lifetime = destroyTime - service.created;
      service.status = 'destroyed';
    }
  }

  getMetrics() {
    return Array.from(this.metrics.values());
  }

  async logDailyReport() {
    const services = this.getMetrics();
    const avgStartupTime =
      services.reduce((sum, s) => sum + (s.startupTime || 0), 0) /
      services.length;
    const successRate =
      services.filter((s) => s.status === 'ready').length / services.length;

    console.log('Daily Koyeb Report:', {
      totalServices: services.length,
      avgStartupTime: `${avgStartupTime}ms`,
      successRate: `${(successRate * 100).toFixed(2)}%`,
      estimatedCost: services.length * 0.000115, // $0.000115 per preview
    });
  }
}
```

#### Health Checks

```typescript
// apps/worker/src/health/koyeb-health.ts
export class KoyebHealthChecker {
  private koyebManager: KoyebServiceManager;

  async checkKoyebStatus(): Promise<boolean> {
    try {
      // Simple API health check
      await this.koyebManager.listServices();
      return true;
    } catch (error) {
      console.error('Koyeb health check failed:', error);
      return false;
    }
  }

  async performPeriodicCleanup() {
    try {
      await this.koyebManager.cleanupOldServices();
    } catch (error) {
      console.error('Periodic cleanup failed:', error);
    }
  }
}
```

#### Logging & Alerts

1. **Service Lifecycle Logging**

   - Log all API calls with timestamps
   - Track service creation → ready → destruction flow
   - Monitor API rate limits and errors

2. **Cost Monitoring**

   - Track number of services created per day
   - Calculate estimated costs
   - Alert if costs exceed thresholds

3. **Performance Metrics**
   - Service startup time (target: <30 seconds)
   - Code injection time (target: <5 seconds)
   - Success rate (target: >95%)

This implementation provides a robust, scalable alternative to the current Docker-based system while maintaining the same user experience with significantly improved performance and cost efficiency.

