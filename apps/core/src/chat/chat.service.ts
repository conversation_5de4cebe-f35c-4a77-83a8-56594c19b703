import { Injectable } from '@nestjs/common';
import { generateObject, streamText, UIMessage } from 'ai';
import { Response } from 'express';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { z } from 'zod';
import { ProjectPlanningService } from '../coreapi/project-planning/project-planning.service';
import { ai_models } from '../utils/models';
import {
  getBasePrompt,
  getCoreFeaturesPrompt,
  getDifficultyRatingPrompt,
  getSummarizationPrompt,
} from '../utils/prompts';

interface ExpressResponseWithFlush extends Response {
  flush?: () => void;
}

interface OnboardingConfig {
  id: string;
  workspaceId: string;
  data: {
    name: string;
    brandColor: string;
    brandVoice: string;
    competitors: string;
    isCompleted: boolean;
    appDescription: string;
    targetCustomers: string;
  };
  createdAt: string;
  updatedAt: string;
}

@Injectable()
export class ChatService {
  constructor(
    private readonly projectPlanningService: ProjectPlanningService,
    private readonly prisma: PrismaService,
  ) {}

  async streamText(
    messages: UIMessage[],
    currentFiles: Record<string, string>,
    onboardingData: Record<string, any> | null | undefined,
    res: ExpressResponseWithFlush,
  ) {
    const _lastMessage = messages[messages.length - 1];
    const _secondLastMessage = messages[messages.length - 2];
    const lastTwoMessages = messages.slice(-2);
    const restOfMessages = messages.slice(0, -2);

    try {
      // const difficulty = await generateObject({
      //   model: ai_models.google['google/gemini-2.5-flash-preview-05-20'],
      //   prompt: getDifficultyRatingPrompt({
      //     message: lastMessage.content,
      //     codeContext: secondLastMessage?.content
      //       ? secondLastMessage.content
      //       : '',
      //   }),
      //   schema: z.object({
      //     rating: z.number(),
      //   }),
      // });

      // const difficultyRating =
      //   messages.length === 1 ? 10 : difficulty.object.rating;

      const conversationHistory = await generateObject({
        model: ai_models.google['google/gemini-2.5-flash-preview-05-20'],
        prompt: getSummarizationPrompt({ messages: restOfMessages }),
        schema: z.object({
          summary: z.string(),
        }),
      });

      // Create the stream
      const result = streamText({
        // model:
        //   difficultyRating < 6
        //     ? ai_models.google['gemini-2.0-flash-001'] // dumbest model
        //     : difficultyRating < 8
        //       ? ai_models.openai['gpt-4o-mini'] // medium model
        //       : ai_models.openai['gpt-4o-mini'], // smartest model
        model: ai_models.anthropic['claude-4-sonnet-20250514'],
        system: getBasePrompt({
          conversationHistorySummary: conversationHistory.object.summary,
          currentFiles,
          onboardingData,
        }),
        messages: [...lastTwoMessages],
        maxTokens: 64000, // Reduced for blueprint
      });

      // Get the response as a Web API Response object
      const aiResponse = result.toDataStreamResponse();

      // Set the same headers as the AI SDK response
      aiResponse.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });

      // Set status code to 200 immediately
      res.status(200);

      // Important: Set headers that prevent buffering
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('X-Accel-Buffering', 'no'); // Helps with Nginx buffering

      // Get the body as a readable stream
      const reader = aiResponse.body?.getReader();

      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      // Safer approach to stream processing using async/await pattern
      const processStream = async () => {
        try {
          let streamActive = true;

          while (streamActive) {
            const { done, value } = await reader.read();

            if (done) {
              streamActive = false;
              break;
            }

            // Write the chunk directly to the response
            res.write(value);

            // Force flush if available
            if (typeof res.flush === 'function') {
              res.flush();
            }
          }

          // End the response when stream is complete
          res.end();
        } catch (streamError) {
          console.error('Error processing stream:', streamError);
          if (!res.headersSent) {
            res.status(500).json({ error: 'Stream processing error' });
          } else {
            res.end();
          }
        }
      };

      // Start processing the stream
      await processStream();
    } catch (error) {
      console.error('Streaming error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Streaming error occurred' });
      } else {
        res.end();
      }
    }
  }

  async streamBlueprintGeneration(
    config: OnboardingConfig,
    res: ExpressResponseWithFlush,
  ) {
    try {
      const blueprintPrompt = ` 
Based on the following app configuration, generate a comprehensive app blueprint:

App Name: ${config.data.name}
Description: ${config.data.appDescription}
Target Customers: ${config.data.targetCustomers}
Brand Voice: ${config.data.brandVoice}
Competitors: ${config.data.competitors}
`;

      // Create the stream
      const result = streamText({
        model: ai_models.google['google/gemini-2.5-flash-preview-05-20'],
        system: getCoreFeaturesPrompt(),
        messages: [
          {
            role: 'user',
            content: blueprintPrompt,
          },
        ],
      });

      // Get the response as a Web API Response object
      const aiResponse = result.toDataStreamResponse();

      // Set the same headers as the AI SDK response
      aiResponse.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });

      // Set status code to 200 immediately
      res.status(200);

      // Important: Set headers that prevent buffering
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('X-Accel-Buffering', 'no'); // Helps with Nginx buffering

      // Get the body as a readable stream
      const reader = aiResponse.body?.getReader();

      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      // Safer approach to stream processing using async/await pattern
      const processStream = async () => {
        try {
          let streamActive = true;

          while (streamActive) {
            const { done, value } = await reader.read();

            if (done) {
              streamActive = false;
              break;
            }

            // Write the chunk directly to the response
            res.write(value);

            // Force flush if available
            if (typeof res.flush === 'function') {
              res.flush();
            }
          }

          // End the response when stream is complete
          res.end();
        } catch (streamError) {
          console.error('Error processing blueprint stream:', streamError);
          if (!res.headersSent) {
            res
              .status(500)
              .json({ error: 'Blueprint stream processing error' });
          } else {
            res.end();
          }
        }
      };

      // Start processing the stream
      await processStream();
    } catch (error) {
      console.error('Blueprint streaming error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Blueprint streaming error occurred' });
      } else {
        res.end();
      }
    }
  }

  async streamTextWithHistory(
    messages: UIMessage[],
    currentFiles: Record<string, string>,
    onboardingData: Record<string, any> | null | undefined,
    workspaceId: string,
    moduleId: string,
    pageId: string,
    userId: number,
    res: ExpressResponseWithFlush,
  ) {
    try {
      const page = await this.projectPlanningService.getPageEntity(
        workspaceId,
        moduleId,
        pageId,
      );

      if (!page) {
        throw new Error(
          `Page not found for workspaceId=${workspaceId}, moduleId=${moduleId}, pageId=${pageId}`,
        );
      }

      const lastMessage = messages[messages.length - 1];
      await this.prisma.pageChatHistory.create({
        data: {
          projectPageId: page.id,
          userId,
          message: lastMessage.content,
          role: lastMessage.role,
        },
      });

      const secondLastMessage = messages[messages.length - 2];
      const lastTwoMessages = messages.slice(-2);
      const restOfMessages = messages.slice(0, -2);

      const difficulty = await generateObject({
        model: ai_models.google['google/gemini-2.5-flash-preview-05-20'],
        prompt: getDifficultyRatingPrompt({
          message: lastMessage.content,
          codeContext: secondLastMessage?.content
            ? secondLastMessage.content
            : '',
        }),
        schema: z.object({
          rating: z.number(),
        }),
      });

      const _difficultyRating =
        messages.length === 1 ? 10 : difficulty.object.rating;

      const conversationHistory = await generateObject({
        model: ai_models.google['google/gemini-2.5-flash-preview-05-20'],
        prompt: getSummarizationPrompt({ messages: restOfMessages }),
        schema: z.object({
          summary: z.string(),
        }),
      });

      const result = streamText({
        model: ai_models.anthropic['claude-4-sonnet-20250514'],
        system: getBasePrompt({
          conversationHistorySummary: conversationHistory.object.summary,
          currentFiles,
          onboardingData,
        }),
        messages: [...lastTwoMessages],
      });

      const aiResponse = result.toDataStreamResponse();

      aiResponse.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });

      res.status(200);

      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('X-Accel-Buffering', 'no');

      const reader = aiResponse.body?.getReader();

      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      let assistantResponse = '';

      const processStream = async () => {
        try {
          let streamActive = true;

          while (streamActive) {
            const { done, value } = await reader.read();

            if (done) {
              streamActive = false;
              break;
            }

            const chunk = new TextDecoder().decode(value);
            assistantResponse += chunk;

            res.write(value);

            if (typeof res.flush === 'function') {
              res.flush();
            }
          }

          // Parse the raw streaming data to extract clean content
          const cleanContent = this.parseStreamingData(assistantResponse);

          await this.prisma.pageChatHistory.create({
            data: {
              projectPageId: page.id,
              userId: null,
              message: cleanContent,
              role: 'assistant',
            },
          });

          res.end();
        } catch (streamError) {
          console.error('Error processing stream:', streamError);
          if (!res.headersSent) {
            res.status(500).json({ error: 'Stream processing error' });
          } else {
            res.end();
          }
        }
      };

      await processStream();
    } catch (error) {
      console.error('Streaming error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Streaming error occurred' });
      } else {
        res.end();
      }
    }
  }

  async getChatHistory(workspaceId: string, moduleId: string, pageId: string) {
    const page = await this.projectPlanningService.getPageEntity(
      workspaceId,
      moduleId,
      pageId,
    );

    if (!page) {
      return [];
    }

    const history = await this.prisma.pageChatHistory.findMany({
      where: { projectPageId: page.id },
      orderBy: { createdAt: 'asc' },
    });

    // Clean up any existing corrupted streaming data in the response
    return history.map((item) => ({
      ...item,
      message:
        item.role === 'assistant'
          ? this.parseStreamingData(item.message)
          : item.message,
    }));
  }

  /**
   * Parse streaming data to extract clean text content
   */
  private parseStreamingData(rawContent: string): string {
    // If the content doesn't look like streaming data, return as-is
    if (!rawContent.includes('0:"') && !rawContent.includes('f:{')) {
      return rawContent;
    }

    const lines = rawContent.split('\n');
    let extractedText = '';

    for (const line of lines) {
      // Look for content lines that start with 0:"
      if (line.startsWith('0:"')) {
        try {
          // Extract the quoted content
          const match = line.match(/^0:"(.*)"/);
          if (match) {
            let content = match[1];
            // Unescape the content
            content = content
              .replace(/\\"/g, '"')
              .replace(/\\n/g, '\n')
              .replace(/\\r/g, '\r')
              .replace(/\\t/g, '\t')
              .replace(/\\\\/g, '\\');
            extractedText += content;
          }
        } catch (error) {
          console.warn('Failed to parse streaming line:', line, error);
        }
      }
    }

    // If we extracted text, return it; otherwise return the original
    return extractedText || rawContent;
  }
}
