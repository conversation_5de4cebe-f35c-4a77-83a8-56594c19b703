import { google } from '@ai-sdk/google';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { requesty } from '@requesty/ai-sdk';
import { LanguageModelV1 } from 'ai';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

type AiModels = {
  openai: {
    'gpt-4o-mini': LanguageModelV1;
    'gpt-costly-o4-mini': LanguageModelV1;
  };
  google: {
    'google/gemini-2.5-flash-preview-05-20': LanguageModelV1;
    'google/gemini-2.5-pro-preview-06-05': LanguageModelV1;
  };
  anthropic: {
    'claude-4-sonnet-20250514': LanguageModelV1;
  };
  deepseek: {
    'deepseek-r1-0528': LanguageModelV1;
  };
};

export const ai_models: AiModels = {
  openai: {
    'gpt-4o-mini': requesty('openai/gpt-4o-mini'),
    'gpt-costly-o4-mini': requesty('openai/o4-mini'),
  },
  google: {
    'google/gemini-2.5-flash-preview-05-20': google(
      'google/gemini-2.5-flash-preview-05-20',
    ),
    'google/gemini-2.5-pro-preview-06-05': requesty(
      'google/gemini-2.5-pro-preview-06-05',
    ),
  },
  deepseek: {
    'deepseek-r1-0528': openrouter('deepseek/deepseek-r1-0528'),
  },
  anthropic: {
    // anthropic's latest model
    // 'claude-4-sonnet-20250514': anthropic('claude-4-sonnet-20250514'),
    'claude-4-sonnet-20250514': requesty('anthropic/claude-sonnet-4-20250514'),
  },
};
